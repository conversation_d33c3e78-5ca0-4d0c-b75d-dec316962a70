package com.bzlj.craft.transform.handle;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import com.bzlj.craft.api.service.IMaterialService;
import com.bzlj.craft.api.service.ITaskMaterialService;
import com.bzlj.craft.entity.*;
import com.bzlj.craft.enums.DictCode;
import com.bzlj.craft.enums.MaterialAttrType;
import com.bzlj.craft.repository.MaterialAttrRepository;
import com.bzlj.craft.service.ISurveillanceService;
import com.bzlj.craft.service.ISysDictItemService;
import com.bzlj.craft.transform.common.CommonHandler;
import com.bzlj.craft.transform.repository.TelegramRepository;
import com.bzlj.craft.transform.service.InputMaterialTransformService;
import com.bzlj.craft.util.JsonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * 投入物料转换处理器
 * <p>
 * 负责处理投入物料消息的转换和处理，主要功能包括：
 * 1. 接收投入物料数据消息
 * 2. 解析物料信息并创建物料实体
 * 3. 处理物料属性和任务物料关联关系
 * 4. 支持批量物料数据处理
 * 5. 异步处理物料数据以提高性能
 * </p>
 * <p>
 * 特别适用于特冶感应炉原料消耗实绩的处理场景
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@MessageHandler(messageType = "input_material", desc = "投入物料转化")
public class InputMaterialTransformHandle extends CommonHandler<String> {

    /**
     * 电报仓储，用于管理电报数据
     */
    @Autowired
    private TelegramRepository telegramRepository;

    @Autowired
    private InputMaterialTransformService inputMaterialTransformService;
    /**
     * 转换处理投入物料消息
     * <p>
     * 接收投入物料的JSON消息，调用具体的物料转换处理方法
     * </p>
     *
     * @param s 投入物料的JSON字符串
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transform(String s, Map<String, Object> handleContext) {
        inputMaterialTransformService.inputMaterialTransform(s);
    }

    /**
     * 清理关联数据
     * <p>
     * 根据电报ID删除相关的电报数据
     * </p>
     *
     * @param telegramId 电报ID，如果为空则不执行删除操作
     */
    @Override
    public void clearRelationData(String telegramId) {
        if(StringUtils.isEmpty(telegramId)) return;
        telegramRepository.deleteById(telegramId);
    }

    /**
     * 处理历史遗留数据
     * <p>
     * 当前实现为空，暂无需处理历史遗留数据
     * </p>
     *
     * @param relationIds 关联ID列表
     */
    @Override
    public void dealLegacyData(List<String> relationIds, Map<String, Object> handleContext) {
        // 暂无需处理历史遗留数据
    }

    /**
     * 获取关联ID列表
     * <p>
     * 当前实现返回空列表，暂无关联ID需要处理
     * </p>
     *
     * @param s 消息载荷
     * @return 空的关联ID列表
     */
    @Override
    public List<String> getRelationIds(String s) {
        return List.of();
    }


}
