package com.bzlj.craft.transform.handle;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import cn.hutool.core.util.BooleanUtil;
import com.bzlj.base.search.SearchCondition;
import com.bzlj.base.search.SearchItem;
import com.bzlj.base.search.SearchItems;
import com.bzlj.craft.api.service.IMaterialService;
import com.bzlj.craft.api.service.IOperationLogService;
import com.bzlj.craft.api.service.ITaskMaterialService;
import com.bzlj.craft.entity.*;
import com.bzlj.craft.enums.DictCode;
import com.bzlj.craft.enums.MaterialAttrType;
import com.bzlj.craft.enums.TaskStatus;
import com.bzlj.craft.repository.MaterialAttrRepository;
import com.bzlj.craft.service.ISurveillanceService;
import com.bzlj.craft.service.ISysDictItemService;
import com.bzlj.craft.transform.common.CommonHandler;
import com.bzlj.craft.transform.data.DataPrepareService;
import com.bzlj.craft.transform.repository.TelegramRepository;
import com.bzlj.craft.util.DateTimeConverter;
import com.bzlj.craft.util.JsonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 生产实绩处理器
 * <p>
 * 负责处理生产实绩消息的转换和处理，主要功能包括：
 * 1. 接收生产实绩数据消息
 * 2. 更新任务状态为已完成
 * 3. 处理产出物料信息和属性
 * 4. 记录操作日志
 * 5. 支持批量生产实绩数据处理
 * 6. 管理任务物料关联关系
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Slf4j
@MessageHandler(messageType = "production_performance", desc = "生产实绩")
public class ProductionPerformanceHandle extends CommonHandler<String> {

    /**
     * 监控服务，用于查询和管理生产任务
     */
    @Autowired
    private ISurveillanceService surveillanceService;

    /**
     * 数据准备服务，用于获取基础数据
     */
    @Autowired
    private DataPrepareService dataPrepareService;

    /**
     * 物料服务，用于管理物料相关业务逻辑
     */
    @Autowired
    private IMaterialService materialService;

    /**
     * 任务物料服务，用于管理任务与物料的关联关系
     */
    @Autowired
    private ITaskMaterialService taskMaterialService;

    /**
     * 系统字典项服务，用于获取字典数据
     */
    @Autowired
    private ISysDictItemService sysDictItemService;

    /**
     * 物料属性仓储，用于管理物料属性数据
     */
    @Autowired
    private MaterialAttrRepository materialAttrRepository;

    /**
     * 操作日志服务，用于记录操作日志
     */
    @Autowired
    private IOperationLogService operationLogService;

    /**
     * 电报仓储，用于管理电报数据
     */
    @Autowired
    private TelegramRepository telegramRepository;

    /**
     * 转换处理生产实绩消息
     * <p>
     * 处理生产实绩JSON数据的核心方法，包括以下步骤：
     * 1. 支持数组格式的批量处理
     * 2. 更新任务状态为已完成
     * 3. 处理任务的开始时间和结束时间
     * 4. 处理产出物料信息
     * 5. 记录操作日志
     * </p>
     *
     * @param json 生产实绩的JSON字符串
     * @throws RuntimeException 当任务不存在时抛出异常
     */
    @Transactional(rollbackFor = Exception.class)
    public void transform(String json, Map<String, Object> handleContext) {
        JsonNode jsonNode = JsonUtils.toJsonNode(json);

        // 支持数组格式的批量处理
        if(jsonNode instanceof ArrayNode){
            jsonNode.forEach(node -> {
                transform(node.toString(), handleContext);
            });
            return;
        }
        if(Objects.isNull(jsonNode.get("taskCode"))
                ||StringUtils.isEmpty(jsonNode.get("taskCode").asText())
                ||StringUtils.isEmpty(jsonNode.get("taskCode").asText().trim())){
            log.error("缺少任务信息");
            return;
        }

        String taskCode = jsonNode.get("taskCode").asText();
        SearchCondition searchCondition = new SearchCondition();
        searchCondition.setSearchItems(SearchItems.builder().item(new SearchItem("taskCode", taskCode, null, SearchItem.Operator.EQ)).build());

        ProductionTask task = surveillanceService.findOne(searchCondition);
        if(Objects.isNull(task)){
            throw new RuntimeException(String.format("任务不存在；任务号：%s", taskCode));
        }

        // 处理开始时间
        if (Objects.nonNull(jsonNode.get("startTime"))) {
            JsonNode startTimeNode = jsonNode.get("startTime");
            if (startTimeNode instanceof ArrayNode) {
                LocalDateTime minStartTime = getMinTimeFromArray((ArrayNode) startTimeNode);
                if (Objects.nonNull(minStartTime)) {
                    task.setStartTime(minStartTime);
                }
            } else {
                task.setStartTime(DateTimeConverter.convertToLocalDateTime(startTimeNode.asText()));
            }
        }

        if (Objects.nonNull(jsonNode.get("endTime"))) {
            JsonNode endTimeNode = jsonNode.get("endTime");
            if (endTimeNode instanceof ArrayNode) {
                LocalDateTime maxEndTime = getMaxTimeFromArray((ArrayNode) endTimeNode);
                if (Objects.nonNull(maxEndTime)) {
                    task.setEndTime(maxEndTime);
                }
            } else {
                task.setEndTime(DateTimeConverter.convertToLocalDateTime(endTimeNode.asText()));
            }
        }
        //更新任务
        ProductionTask productionTask = surveillanceService.updateEntity(task);
        // 更新任务状态为已完成
        surveillanceService.changeStatus(productionTask.getTaskId(), dataPrepareService.getStatusDictItem(TaskStatus.completed.getCode()));
//        ArrayNode inputMaterialNodes = (ArrayNode) jsonNode.get("inputMaterials");
        //保存投入物料
//        dealInputMaterials(inputMaterialNodes, productionTask);
        List<SysDictItem> attrType = sysDictItemService.findEntityByDictCode(DictCode.MATERIAL_ATTR_TYPE.getCode());
        ImmutableMap<String, SysDictItem> attrTypeMap = Maps.uniqueIndex(attrType, SysDictItem::getItemCode);
        //保存产出物料

        boolean inputEQOutput = Objects.nonNull(jsonNode.get("inputEQOutput")) && jsonNode.get("inputEQOutput").asBoolean();

        // 处理outputMaterials节点，确保它是ArrayNode类型
        ArrayNode outputMaterialsArray;
        JsonNode outputMaterialsNode = jsonNode.get("outputMaterials");
        if (outputMaterialsNode instanceof ArrayNode) {
            outputMaterialsArray = (ArrayNode) outputMaterialsNode;
        } else if (Objects.nonNull(outputMaterialsNode)) {
            // 如果不是ArrayNode，创建一个新的ArrayNode并将原节点添加进去
            outputMaterialsArray = (ArrayNode) JsonUtils.toJsonNode("[]");
            outputMaterialsArray.add(outputMaterialsNode);
        } else {
            // 如果节点为null，创建一个空的ArrayNode
            outputMaterialsArray = (ArrayNode) JsonUtils.toJsonNode("[]");
        }

        dealOutputMaterials(outputMaterialsArray, productionTask, attrTypeMap, inputEQOutput);

        //保存生产实绩（操作记录）
        dealOperationLog(jsonNode.get("operationLog"), productionTask);

        //todo 保存班组信息
        dealTeamInfo(jsonNode.get("teamInfo"), productionTask);
    }

    @Override
    public void dealLegacyData(List<String> relationIds, Map<String, Object> handleContext) {

    }

    @Override
    public List<String> getRelationIds(String s) {
        return List.of();
    }

    @Override
    public void clearRelationData(String telegramId) {
        if(StringUtils.isEmpty(telegramId)) return;
        telegramRepository.deleteById(telegramId);
    }

    /*private void dealInputMaterials(ArrayNode inputMaterialNodes, ProductionTask productionTask) {
        if (Objects.nonNull(inputMaterialNodes) && !inputMaterialNodes.isEmpty()) {
            List<String> inputMaterials = StreamSupport.stream(inputMaterialNodes.spliterator(), false)
                    .map(JsonNode::asText).distinct()
                    .collect(Collectors.toList());
            List<Material> materials = materialService.findByMaterialCodes(inputMaterials);
            List<String> existMaterials = materials.stream().map(Material::getMaterialCode).collect(Collectors.toList());
            inputMaterials.removeAll(existMaterials);
            if (!CollectionUtils.isEmpty(inputMaterials)) {
                List<Material> materialList = inputMaterials.stream().map(inputMaterial -> {
                    Material material = new Material();
                    //todo 其他属性暂无
                    material.setMaterialCode(inputMaterial);
                    material.setMaterialName(inputMaterial);
                    return material;
                }).collect(Collectors.toList());
                materials.addAll(materialService.batchInsertEntity(materialList));
            }
            //创建投入物料
            buildTaskMaterialAndSave(materials, productionTask, false);
        }
    }*/

    private void dealOutputMaterials(ArrayNode outputMaterialNodes, ProductionTask productionTask, Map<String, SysDictItem> attrTypeMap,Boolean inputEQOutput) {
        if (Objects.nonNull(outputMaterialNodes) && !outputMaterialNodes.isEmpty()) {
            Map<String, List<MaterialAttr>> materialAttrMap = new HashMap<>();
            List<Material> materials = new ArrayList<>();
            outputMaterialNodes.iterator().forEachRemaining(outputMaterialNode -> {
                Material material = new Material();
                if(Objects.isNull(outputMaterialNode.get("materialCode")) || StringUtils.isEmpty(outputMaterialNode.get("materialCode").asText())){
                    return;
                }
                material.setMaterialCode(outputMaterialNode.get("materialCode").asText());
                if(Objects.isNull(outputMaterialNode.get("materialName"))){
                    material.setMaterialName(outputMaterialNode.get("materialCode").asText());
                }else{
                    material.setMaterialName(outputMaterialNode.get("materialName").asText());
                }
                material.setMaterialType("实绩");
                if(Objects.nonNull(outputMaterialNode.get("brand"))){
                    material.setBrand((outputMaterialNode.get("brand").asText()));
                }
                if(Objects.nonNull(outputMaterialNode.get("heatNumber"))){
                    material.setHeatNumber((outputMaterialNode.get("heatNumber").asText()));
                }
                materials.add(material);
                List<MaterialAttr> materialAttrs = materialAttrMap.getOrDefault(material.getMaterialCode(), new ArrayList<>());
                if (Objects.nonNull(outputMaterialNode.get("physicalAttr"))) {
                    MaterialAttr materialAttr = new MaterialAttr();
                    materialAttr.setAttr(JsonUtils.nodeToMap(outputMaterialNode.get("physicalAttr")));
                    materialAttr.setAttrType(attrTypeMap.get(MaterialAttrType.physical_attr.getCode()));
                    materialAttrs.add(materialAttr);
                    materialAttrMap.put(material.getMaterialCode(), materialAttrs);
                }
                if (Objects.nonNull(outputMaterialNode.get("specificationAttr"))) {
                    MaterialAttr materialAttr = new MaterialAttr();
                    materialAttr.setAttr(JsonUtils.nodeToMap(outputMaterialNode.get("specificationAttr")));
                    materialAttr.setAttrType(attrTypeMap.get(MaterialAttrType.specification_attr.getCode()));
                    materialAttrs.add(materialAttr);
                    materialAttrMap.put(material.getMaterialCode(), materialAttrs);
                }
            });
            List<Material> materialList = materialService.batchInsertEntity(materials);
            if (!CollectionUtils.isEmpty(materialAttrMap)) {
                materialList.forEach(material -> {
                    List<MaterialAttr> attrs = materialAttrMap.get(material.getMaterialCode());
                    if (Objects.nonNull(attrs)) {
                        attrs.forEach(materialAttr -> {
                            materialAttr.setMaterial(material);
                        });
                    }
                });
                materialAttrRepository.saveAll(materialAttrMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList()));
            }
            taskMaterialService.buildTaskMaterialAndSave(materialList, productionTask, true,materialAttrMap);
            if(BooleanUtil.isTrue(inputEQOutput)){
                taskMaterialService.buildTaskMaterialAndSave(materialList, productionTask, false,materialAttrMap);
            }

        }
    }


    private void dealOperationLog(JsonNode jsonNode, ProductionTask productionTask) {
        if (Objects.isNull(jsonNode)) return;
        OperationLog operationLog = new OperationLog();
        operationLog.setTask(productionTask);
        operationLog.setDescription(JsonUtils.toMap(jsonNode));
        operationLog.setOperationType("-");
        operationLogService.insertEntity(operationLog);
    }

    /**
     * 处理班组信息
     * <p>
     * 处理生产实绩中的班组相关信息
     * 注意：当前实现为空，具体的班组信息处理逻辑待实现
     * </p>
     *
     * @param jsonNode 包含班组信息的JSON节点
     * @param productionTask 生产任务实体
     */
    private void dealTeamInfo (JsonNode jsonNode, ProductionTask productionTask) {
        if (Objects.isNull(jsonNode)) return;

        // TODO: 实现具体的班组信息处理逻辑
    }

    /**
     * 从时间数组中获取最小时间值（去除空值）
     * @param arrayNode 时间数组节点
     * @return 最小时间值，如果数组为空或所有值都为空则返回null
     */
    private LocalDateTime getMinTimeFromArray(ArrayNode arrayNode) {
        if (Objects.isNull(arrayNode) || arrayNode.isEmpty()) {
            return null;
        }

        LocalDateTime minTime = null;
        for (JsonNode timeNode : arrayNode) {
            if (Objects.nonNull(timeNode) && !timeNode.isNull() &&
                StringUtils.isNotBlank(timeNode.asText())) {
                try {
                    LocalDateTime currentTime = DateTimeConverter.convertToLocalDateTime(timeNode.asText());
                    if (Objects.isNull(minTime) || currentTime.isBefore(minTime)) {
                        minTime = currentTime;
                    }
                } catch (Exception e) {
                    // 忽略无法解析的时间值，继续处理下一个
                }
            }
        }
        return minTime;
    }

    /**
     * 从时间数组中获取最大时间值（去除空值）
     * @param arrayNode 时间数组节点
     * @return 最大时间值，如果数组为空或所有值都为空则返回null
     */
    private LocalDateTime getMaxTimeFromArray(ArrayNode arrayNode) {
        if (Objects.isNull(arrayNode) || arrayNode.isEmpty()) {
            return null;
        }

        LocalDateTime maxTime = null;
        for (JsonNode timeNode : arrayNode) {
            if (Objects.nonNull(timeNode) && !timeNode.isNull() &&
                StringUtils.isNotBlank(timeNode.asText())) {
                try {
                    LocalDateTime currentTime = DateTimeConverter.convertToLocalDateTime(timeNode.asText());
                    if (Objects.isNull(maxTime) || currentTime.isAfter(maxTime)) {
                        maxTime = currentTime;
                    }
                } catch (Exception e) {
                    // 忽略无法解析的时间值，继续处理下一个
                }
            }
        }
        return maxTime;
    }
}
